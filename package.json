{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@headlessui/react": "^1.7.11", "@tailwindcss/forms": "^0.5.3", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@tinymce/tinymce-react": "^4.2.0", "@types/jest": "^27.5.2", "@types/node": "^16.11.56", "@types/react": ">=18.0.18", "@types/react-dom": ">=18.0.6", "@types/react-share": "^4.0.0", "axios": "^1.1.3", "countries-list": "^2.6.1", "dotenv": "^16.0.3", "emoji-picker-react": "^4.4.7", "flag-icon-css": "^4.1.7", "flowbite": "^1.6.4", "formik": "^2.2.9", "js-file-download": "^0.4.12", "jspdf": "^2.5.1", "react": "^18.2.0", "react-app-rewired": "^2.2.1", "react-circle-flags": "^0.0.18", "react-country-flag": "^3.0.2", "react-detect-click-outside": "^1.1.7", "react-dom": "^18.2.0", "react-flags-select": "^2.2.3", "react-ga4": "^2.1.0", "react-icons": "^4.4.0", "react-pdf": "^6.2.0", "react-player": "^2.11.0", "react-redux": "^8.0.5", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "react-share": "^4.4.1", "react-slick": "^0.29.0", "react-toastify": "^9.1.2", "react-tooltip": "^5.10.0", "react-transition-group": "^4.4.5", "redux": "^4.2.0", "redux-thunk": "^2.4.2", "slick-carousel": "^1.8.1", "typescript": "^4.8.2", "web-vitals": "^2.1.4", "yup": "^0.32.11"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@svgr/webpack": "^6.2.1", "@types/react-helmet": "^6.1.11", "@types/react-pdf": "^6.2.0", "@types/react-redux": "^7.1.24", "@types/react-slick": "^0.23.10", "@types/react-transition-group": "^4.4.11", "@types/redux": "^3.6.0", "@types/redux-thunk": "^2.1.0", "autoprefixer": "^10.4.8", "postcss": "^8.4.16", "react-helmet": "^6.1.0", "tailwindcss": "^3.1.8"}, "overrides": {"@svgr/webpack": "$@svgr/webpack"}}