import React, { useEffect, useRef, useState } from "react";

interface AnimateCountInterface {
  targetValue: number;
  title: string;
}

// Function to round to the nearest "nice" value
const roundToNiceValue = (value: number): number => {
  if (value >= 500) {
    // Round to nearest 50 for values >= 500
    return Math.round(value / 50) * 50;
  } else if (value >= 100) {
    // Round to nearest 10 for values >= 100
    return Math.round(value / 10) * 10;
  } else {
    // Round to nearest 5 for smaller values
    return Math.round(value / 5) * 5;
  }
};

const AnimateCount: React.FC<AnimateCountInterface> = ({
  targetValue,
  title,
}) => {
  // Round the target value to a nice number
  const roundedTargetValue = roundToNiceValue(targetValue);

  const duration = 1000,
    start = 1;
  const countRef = useRef<HTMLDivElement>(null);

  const [count, setCount] = useState<number>(1);

  const animateCount = () => {
    const startTime = Date.now();
    const endTime = startTime + duration;

    const updateCount = () => {
      const now = Date.now();
      const remainingTime = Math.max(0, endTime - now);
      const delta = roundedTargetValue - start;
      const step = (delta / duration) * (duration - remainingTime);

      setCount(Math.floor(start + step));

      if (remainingTime > 0) {
        requestAnimationFrame(updateCount);
      }
    };

    requestAnimationFrame(updateCount);
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            animateCount();
            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.5 }
    );

    const countRefCurrent = countRef.current;
    if (countRefCurrent) {
      observer.observe(countRefCurrent);
    }

    return () => {
      if (countRefCurrent) {
        observer.unobserve(countRefCurrent);
      }
    };
  }, []);

  return (
    <div
      className="flex flex-col justify-center items-center gap-1 w-full"
      ref={countRef}
    >
      <h1 className="text-2xl md:text-4xl font-extrabold">{count}+</h1>
      <p className="text-xs md:text-lg font-light text-center">{title}</p>
    </div>
  );
};

export default AnimateCount;
