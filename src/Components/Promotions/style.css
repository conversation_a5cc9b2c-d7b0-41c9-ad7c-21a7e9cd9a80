.product-list-main {
  @apply grid w-full md:px-10 mx-auto lg:gap-x-5 gap-x-10 gap-y-10 lg:grid-cols-3 sm:grid-cols-2 grid-cols-1;
}

.product-card-title {
  @apply flex text-left px-4 space-y-1;
}

.product-card-main {
  @apply flex flex-col justify-start sm:m-10 duration-150 border md:pb-2 shadow-lg hover:shadow-lg hover:shadow-GTI-BLUE-default space-y-1 rounded-xl cursor-pointer;
}

.product-card-img {
  @apply flex w-full duration-150;
}

* {
  box-sizing: border-box;
  font-family: Arial, sans-serif;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 30px;
}

/* Dropdown styles */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: #ffffff;
  border: 1px solid #ccc;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
  border-radius: 4px;
}

.dropdown-toggle:hover {
  background-color: #f8f8f8;
}

.dropdown-option-image {
  width: 40px;
  height: 40px;
  margin-right: 12px;
  border-radius: 50%;
}

.dropdown-option-label {
  flex-grow: 1;
  font-weight: bold;
}

.dropdown-caret {
  display: inline-block;
  margin-left: 12px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 8px 8px 0 8px;
  border-color: #999 transparent transparent transparent;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  margin: 0;
  padding: 12px 0;
  background-color: #fff;
  border: 1px solid #ccc;
  border-top: none;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  list-style: none;
  z-index: 1;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-12px);
  transition: opacity 0.2s ease, visibility 0.2s ease, transform 0.2s ease;
  max-height: 200px;
  overflow-y: auto;
  border-radius: 4px;
}

.dropdown-menu.open {
  opacity: 1;
  visibility: visible;
}

.dropdown-menu li {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.dropdown-menu li:hover {
  background-color: #f8f8f8;
}

.dropdown-menu li .dropdown-option-image {
  margin-right: 12px;
}

.dropdown-menu li .dropdown-option-label {
  flex-grow: 1;
  font-weight: bold;
}

/* Centering the dropdown */
.dropdown.open .dropdown-menu {
  transform: translateY(0);
}

/* Adjust dropdown width */
.dropdown.open .dropdown-toggle {
  border-radius: 4px 4px 0 0;
  border-bottom: none;
}

.dropdown.open .dropdown-menu {
  border-radius: 0 0 4px 4px;
  border-top: none;
}
