/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{js,jsx,ts,tsx}", "./node_modules/flowbite/**/*.js"],
  theme: {
    extend: {
      fontFamily: {
        roboto: ["Roboto", "sans-serif"],
        "dm-sans": ["DM Sans", "sans-serif"],
        montserrat: ["Montserrat", "sans-serif"],
        "pj-sans": ["Plus Jakarta Sans", "sans-serif"],
        popins: ["Poppins", "sans-serif"],
        "zilla-slab": ["Zilla Slab", "serif"],
        sans: [
          "ui-sans-serif",
          "system-ui",
          "-apple-system",
          "BlinkMacSystemFont",
          "Segoe UI",
          "Roboto",
          "Helvetica Neu",
          "Arial",
          "Noto Sans",
          "sans-serif",
          "Apple Color Emoji",
          "Segoe UI Emoji",
          "Segoe UI Symbol",
          "Noto Color Emoji",
        ],
      },
      screens: {
        "1300px": "1300px",
      },
      transitionProperty: {
        left: "left",
        all: "all",
      },
      colors: {
        "GTI-BLUE": {
          default: "#151E70",
        },
      },
      variants: {
        extend: {
          display: ["group-hover"],
        },
      },
      fontSize: {
        xxs: "0.5rem",
        xxxs: "0.2rem",
      },
    },
  },
  plugins: [require("@tailwindcss/forms"), require("flowbite/plugin")],
};
